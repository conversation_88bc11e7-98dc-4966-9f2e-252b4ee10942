package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Share Link Read Operations

// FindShareLink retrieves a share link by ID
func (m *mongoDB) FindShareLink(ctx context.Context, id primitive.ObjectID) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link", errors.Internal, err)
	}
	return &shareLink, nil
}

// FindShareLinkByToken retrieves a share link by token
func (m *mongoDB) FindShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"token": token}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link", errors.Internal, err)
	}
	return &shareLink, nil
}

// FindShareLinkByDreamID retrieves a share link by dream ID
func (m *mongoDB) FindShareLinkByDreamID(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	var shareLink model.ShareLink
	err := m.shareLinksCollection.FindOne(ctx, bson.M{"dreamId": dreamID}).Decode(&shareLink)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "share link not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find share link", errors.Internal, err)
	}
	return &shareLink, nil
}

// Share Link Write Operations

// CreateShareLink creates a new share link
func (m *mongoDB) CreateShareLink(ctx context.Context, shareLink *model.ShareLink) (string, error) {
	shareLink.CreatedAt = time.Now()
	shareLink.UpdatedAt = shareLink.CreatedAt

	insertedResult, err := m.shareLinksCollection.InsertOne(ctx, shareLink)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return "", errors.New(errors.Repository, "share link token already exists", errors.Conflict, err)
		}
		return "", errors.New(errors.Repository, "failed to create share link", errors.Internal, err)
	}

	shareLink.ObjectID = insertedResult.InsertedID.(primitive.ObjectID)
	return shareLink.ObjectID.Hex(), nil
}

// UpdateShareLink updates an existing share link
func (m *mongoDB) UpdateShareLink(ctx context.Context, shareLink *model.ShareLink) error {
	shareLink.UpdatedAt = time.Now()

	filter := bson.M{"_id": shareLink.ObjectID}
	update := bson.M{"$set": shareLink}

	result, err := m.shareLinksCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update share link", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "share link not found", errors.NotFound, nil)
	}

	return nil
}

// DeleteShareLink deletes a share link by ID
func (m *mongoDB) DeleteShareLink(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.shareLinksCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete share link", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "share link not found", errors.NotFound, nil)
	}

	return nil
}
