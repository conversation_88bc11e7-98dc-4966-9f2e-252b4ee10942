package dreamboard

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/labstack/echo/v4"
)

// RegisterSharedDreamRoutes registers shared dream related routes
func (dc *controller) RegisterSharedDreamRoutes(ctx context.Context, currentGroup *echo.Group) {
	// Shared Dreams routes
	sharedDreamsGroup := currentGroup.Group("/dreamboards/dreams/shared", middlewares.AuthGuard())
	
	// Invite Management
	sharedDreamsGroup.GET("/invite/:token", dc.GetInviteDetails())
	sharedDreamsGroup.POST("/join", dc.JoinSharedDream())
	
	// Dream Dashboard
	sharedDreamsGroup.GET("/:dreamId/dashboard", dc.GetDreamDashboard())
	
	// Share Link Management
	sharedDreamsGroup.POST("/:dreamId/share-link", dc.CreateShareLink())
	sharedDreamsGroup.GET("/:dreamId/share-link", dc.GetShareLink())
	sharedDreamsGroup.PUT("/:dreamId/share-link/status", dc.UpdateShareLinkStatus())
	sharedDreamsGroup.POST("/:dreamId/share-link/regenerate", dc.RegenerateShareLink())
}

// GetInviteDetails retrieves invitation details for a share link token
func (dc *controller) GetInviteDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		token := c.Param("token")

		if token == "" {
			return errors.New(errors.Controller, "token is required", errors.BadRequest, nil)
		}

		inviteDetails, err := dc.Service.GetInviteDetails(ctx, token)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, inviteDetails)
	}
}

// JoinSharedDream allows a user to join a shared dream
func (dc *controller) JoinSharedDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var request struct {
			Token                string          `json:"token" validate:"required"`
			MonthlyPledgedAmount monetary.Amount `json:"monthlyPledgedAmount" validate:"required,min=1"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Validate input
		if request.Token == "" {
			return errors.New(errors.Controller, "token is required", errors.BadRequest, nil)
		}
		if request.MonthlyPledgedAmount <= 0 {
			return errors.New(errors.Controller, "monthly pledged amount must be greater than 0", errors.BadRequest, nil)
		}

		contribution, err := dc.Service.JoinSharedDream(ctx, request.Token, userToken.Uid, false, request.MonthlyPledgedAmount)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, contribution)
	}
}

// GetDreamDashboard retrieves comprehensive dashboard information for a shared dream
func (dc *controller) GetDreamDashboard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		dashboard, err := dc.Service.GetDreamDashboard(ctx, dreamID, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, dashboard)
	}
}

// CreateShareLink creates a new share link for a dream
func (dc *controller) CreateShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.CreateShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, shareLink)
	}
}

// GetShareLink retrieves a share link by dream ID
func (dc *controller) GetShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.GetShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, shareLink)
	}
}

// UpdateShareLinkStatus updates the enabled status of a share link
func (dc *controller) UpdateShareLinkStatus() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		var request struct {
			IsEnabled bool `json:"isEnabled"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		err := dc.Service.UpdateShareLinkStatus(ctx, dreamID, request.IsEnabled)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// RegenerateShareLink creates a new token for an existing share link
func (dc *controller) RegenerateShareLink() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		shareLink, err := dc.Service.RegenerateShareLink(ctx, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, shareLink)
	}
}
