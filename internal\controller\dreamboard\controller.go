package dreamboard

import (
	"context"
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/dreamboard"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)
	RegisterSharedDreamRoutes(ctx context.Context, currentGroup *echo.Group)
	RegisterContributionRoutes(ctx context.Context, currentGroup *echo.Group)

	// CRUD
	Find() echo.HandlerFunc
	FindAll() echo.HandlerFunc
	FindByUser() echo.HandlerFunc
	Update() echo.HandlerFunc
	Delete() echo.HandlerFunc

	// Category CRUD
	CreateCategory() echo.HandlerFunc
	FindCategory() echo.HandlerFunc
	UpdateCategory() echo.HandlerFunc
	DeleteCategory() echo.HandlerFunc

	// Dream CRUD
	CreateDream() echo.HandlerFunc
	FindDream() echo.HandlerFunc
	FindPersonalDreams() echo.HandlerFunc
	FindSharedDreams() echo.HandlerFunc
	FindAllDreams() echo.HandlerFunc
	UpdateDream() echo.HandlerFunc
	PatchDream() echo.HandlerFunc
	RemoveDream() echo.HandlerFunc

	// Shared Dreams
	GetInviteDetails() echo.HandlerFunc
	JoinSharedDream() echo.HandlerFunc
	GetDreamDashboard() echo.HandlerFunc
	CreateShareLink() echo.HandlerFunc
	GetShareLink() echo.HandlerFunc
	UpdateShareLinkStatus() echo.HandlerFunc
	RegenerateShareLink() echo.HandlerFunc

	// Contributions
	GetContributionsByDreamID() echo.HandlerFunc
	GetMyContributions() echo.HandlerFunc
	UpdateContribution() echo.HandlerFunc
	DeleteContribution() echo.HandlerFunc
	UpdateContributionStatus() echo.HandlerFunc

	// Utility
	Initialize() echo.HandlerFunc
}

type controller struct {
	Service dreamboard.Service
}

func New(service dreamboard.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (dc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	dreamboardsGroup := currentGroup.Group("/dreamboards", middlewares.AuthGuard())

	// CRUD
	dreamboardsGroup.POST("", dc.Create(), middlewares.AdminGuard())
	dreamboardsGroup.GET("/:id", dc.Find(), middlewares.AdminGuard())
	dreamboardsGroup.GET("", dc.FindAll(), middlewares.AdminGuard())
	dreamboardsGroup.GET("/me", dc.FindByUser())
	dreamboardsGroup.PUT("/:id", dc.Update(), middlewares.AdminGuard())
	dreamboardsGroup.DELETE("/:id", dc.Delete(), middlewares.AdminGuard())

	// Category CRUD
	categoriesGroup := dreamboardsGroup.Group("/:boardId/categories")
	categoriesGroup.POST("", dc.CreateCategory(), middlewares.AdminGuard())
	categoriesGroup.GET("/:id", dc.FindCategory(), middlewares.AdminGuard())
	categoriesGroup.PUT("/:id", dc.UpdateCategory(), middlewares.AdminGuard())
	categoriesGroup.DELETE("/:id", dc.DeleteCategory(), middlewares.AdminGuard())

	// Dream CRUD
	dreamsGroup := dreamboardsGroup.Group("/dreams")
	dreamsGroup.POST("", dc.CreateDream())
	dreamsGroup.GET("/:id", dc.FindDream())
	dreamsGroup.GET("", dc.FindAllDreams())
	dreamsGroup.GET("/personal", dc.FindPersonalDreams())
	dreamsGroup.GET("/shared", dc.FindSharedDreams())
	dreamsGroup.PUT("/:id", dc.UpdateDream())
	dreamsGroup.PATCH("/:id", dc.PatchDream())
	dreamsGroup.DELETE("/:id", dc.RemoveDream())

	// Utility
	dreamboardsGroup.POST("/initialize", dc.Initialize(), middlewares.AdminGuard())

	// Register shared dream and contribution routes
	dc.RegisterSharedDreamRoutes(ctx, currentGroup)
	dc.RegisterContributionRoutes(ctx, currentGroup)
}

// CRUD
func (dc *controller) Create() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		_, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		var dreamboard model.Dreamboard
		if err := c.Bind(&dreamboard); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		dreamboardID, err := dc.Service.Create(ctx, &dreamboard)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, dreamboardID)
	}
}

func (dc *controller) Find() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Get optional query parameters
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Filter dreams based on query parameters
		filteredDreams := []*model.Dream{}
		for _, dream := range dreamboard.Dreams {
			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		dreamboard.Dreams = filteredDreams

		return c.JSON(http.StatusOK, dreamboard)
	}
}

func (dc *controller) FindAll() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		dreamboard, err := dc.Service.FindAll(ctx)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, dreamboard)
	}
}

func (dc *controller) FindByUser() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get optional query parameters
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Filter dreams based on query parameters
		filteredDreams := []*model.Dream{}
		for _, dream := range dreamboard.Dreams {
			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		dreamboard.Dreams = filteredDreams

		return c.JSON(http.StatusOK, dreamboard)
	}
}

func (dc *controller) Update() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		var dreamboard model.Dreamboard
		if err := c.Bind(&dreamboard); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, nil)
		}
		dreamboard.ID = boardID

		if err := dc.Service.Update(ctx, &dreamboard); err != nil {
			return err
		}

		// Find the updated dreamboardboard to double check the updates
		updatedBoard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedBoard)
	}
}

func (dc *controller) Delete() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		if err := dc.Service.Delete(ctx, boardID); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Category CRUD
func (dc *controller) CreateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can create categories
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only manage categories in your own dreamboard", errors.Forbidden, nil)
		}

		var category model.Category
		if err := c.Bind(&category); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		createdCategory, err := dc.Service.CreateCategory(ctx, dreamboard, &category)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdCategory)
	}
}

func (dc *controller) FindCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		category, err := dc.Service.FindCategory(ctx, dreamboard, categoryID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, category)
	}
}

func (dc *controller) UpdateCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can update categories
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only manage categories in your own dreamboard", errors.Forbidden, nil)
		}

		var category model.Category
		if err := c.Bind(&category); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		category.ID = categoryID

		updatedCategory, err := dc.Service.UpdateCategory(ctx, dreamboard, &category)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedCategory)
	}
}

func (dc *controller) DeleteCategory() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		boardID, err := getParam(c, "boardId")
		if err != nil {
			return err
		}

		categoryID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.Find(ctx, boardID)
		if err != nil {
			return err
		}

		// Authorization: Only admins or the dreamboard owner can delete categories
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only manage categories in your own dreamboard", errors.Forbidden, nil)
		}

		if err := dc.Service.DeleteCategory(ctx, dreamboard, categoryID); err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// Dream CRUD
func (dc *controller) CreateDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream model.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// User creator ID is always the user creating the dreamboard
		dream.CreatorUserID = userToken.Uid

		createdDream, err := dc.Service.CreateDream(ctx, dreamboard, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdDream)
	}
}

func (dc *controller) FindDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		dream, err := dc.Service.FindDream(ctx, dreamboard, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, dream)
	}
}

func (dc *controller) FindAllDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get optional query parameters
		category, _ := getParam(c, "category")
		log.Println("category", category)
		title, _ := getParam(c, "title")
		timeFrame, _ := getParam(c, "timeFrame")
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Filter dreams based on query parameters
		filteredDreams := []*model.Dream{}
		for _, dream := range dreamboard.Dreams {
			if category != "" && dream.Category.String() != category {
				continue
			}

			if title != "" && !strings.Contains(strings.ToLower(dream.Title), strings.ToLower(title)) {
				continue
			}

			if timeFrame != "" && string(dream.TimeFrame.String()) != timeFrame {
				continue
			}

			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		dreamboard.Dreams = filteredDreams

		return c.JSON(http.StatusOK, dreamboard.Dreams)
	}
}

// FindPersonalDreams retrieves all personal (non-shared) dreams for the authenticated user
func (dc *controller) FindPersonalDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		personalDreams, err := dc.Service.FindPersonalDreams(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, personalDreams)
	}
}

// FindSharedDreams retrieves all shared dreams where the user is creator or active contributor
func (dc *controller) FindSharedDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		sharedDreams, err := dc.Service.FindSharedDreams(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, sharedDreams)
	}
}

func (dc *controller) UpdateDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream model.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		dream.ID = dreamID

		// Authorization: Only admins or the dreamboard owner can update dreams
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only update your own dreams", errors.Forbidden, nil)
		}

		updatedDreams, err := dc.Service.UpdateDream(ctx, dreamboard, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}

// PatchDream handles PATCH requests to partially update a dream
func (dc *controller) PatchDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get dream ID from URL
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		// Get user session token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get the user's dreamboard
		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get the current dream
		currentDream, err := dc.Service.FindDream(ctx, dreamboard, dreamID)
		if err != nil {
			return err
		}

		// Bind the patch data
		var patch model.Dream
		if err := c.Bind(&patch); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		patch.ID = dreamID

		// Merge the patch into current dream
		mergedDream := mergeDreams(currentDream, &patch)

		// Authorization check
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only update your own dreams", errors.Forbidden, nil)
		}

		// Update using the merged dream
		updatedDreams, err := dc.Service.UpdateDream(ctx, dreamboard, mergedDream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}

func (dc *controller) RemoveDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		dreamboard, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream model.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		dream.ID = dreamID

		// Authorization: Only admins or the dreamboard owner can delete dreams
		if userToken.Role != "admin" && userToken.Uid != dreamboard.User {
			return errors.New(errors.Controller, "you can only delete your own dreams", errors.Forbidden, nil)
		}

		updatedDreams, err := dc.Service.RemoveDream(ctx, dreamboard, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}

// Utility
func (dc *controller) Initialize() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		var user model.User
		if err := c.Bind(&user); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if err := dc.Service.Initialize(ctx, user.ID); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, nil)
	}
}

// Helper
// getParam Generic helper to get and validate parameters
func getParam(c echo.Context, key string) (string, error) {
	value := strings.TrimSpace(c.Param(key)) // Try getting from path parameters
	if value == "" {
		value = strings.TrimSpace(c.QueryParam(key)) // Try getting from query parameters
	}

	if value == "" {
		return "", errors.New(errors.Controller, "missing or invalid param", errors.Validation, nil)
	}

	return value, nil
}

// Helper
// reverse the order of dreams in a dreamboard
func reverse(dreams []*model.Dream) {
	for i, j := 0, len(dreams)-1; i < j; i, j = i+1, j-1 {
		dreams[i], dreams[j] = dreams[j], dreams[i]
	}
}

// Helper
// mergeDreams merges non-zero fields from patch into current dream
func mergeDreams(current, patch *model.Dream) *model.Dream {
	// Merge title if provided
	if patch.Title != "" {
		current.Title = patch.Title
	}

	// Merge category if provided and valid
	if patch.Category != model.CategoryIdentifierUndefined {
		current.Category = patch.Category
	}

	// Merge time frame if provided and valid
	if patch.TimeFrame != model.UndefinedTimeFrame {
		current.TimeFrame = patch.TimeFrame
	}

	// Merge deadline if provided and not zero
	if !patch.Deadline.IsZero() {
		current.Deadline = patch.Deadline
	}

	// Merge costs if provided and non-negative
	if patch.EstimatedCost >= 0 {
		current.EstimatedCost = patch.EstimatedCost
	}
	if patch.MonthlySavings >= 0 {
		current.MonthlySavings = patch.MonthlySavings
	}

	// Merge money sources if provided
	if len(patch.MoneySource) > 0 {
		current.MoneySource = patch.MoneySource
	}

	// Merge custom money source if provided
	if patch.CustomMoneySource != "" {
		current.CustomMoneySource = patch.CustomMoneySource
	}

	// For booleans like Completed, we override since we can't distinguish
	// between "false" and "not provided" in JSON
	current.Completed = patch.Completed

	return current
}
