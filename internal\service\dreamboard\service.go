package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"

	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error)
	Find(ctx context.Context, id string) (*model.Dreamboard, error)
	FindAll(ctx context.Context) ([]*model.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error)
	Update(ctx context.Context, dreamboard *model.Dreamboard) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error)
	FindCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) (*model.Category, error)
	UpdateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error)
	DeleteCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) error

	// Dream CRUD
	CreateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) (*CreateDreamResponseDTO, error)
	FindDream(ctx context.Context, dreamboard *model.Dreamboard, dreamID string) (*model.Dream, error)
	UpdateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error)
	RemoveDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error)

	// Shared Dreams
	FindPersonalDreams(ctx context.Context, userID string) ([]*model.Dream, error)
	FindSharedDreams(ctx context.Context, userID string) ([]*model.Dream, error)

	// Share Link Management
	CreateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)
	GetShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)
	GetShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error)
	UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error
	RegenerateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error)

	// Invitation Management
	GetInviteDetails(ctx context.Context, token string) (*InviteDetailsDTO, error)
	JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*model.Contribution, error)

	// Contribution Management
	GetContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	GetActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	GetContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error)
	GetContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error)
	CreateContribution(ctx context.Context, contribution *model.Contribution) (*model.Contribution, error)
	UpdateContribution(ctx context.Context, contribution *model.Contribution) error
	UpdateContributionStatus(ctx context.Context, dreamID string, status model.ContributionStatus) error
	DeleteContribution(ctx context.Context, contributionID string) error

	// Dream Management
	CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error)
	GetDreamDashboard(ctx context.Context, dreamID string, userID string) (*DreamDashboardDTO, error)

	// Utility
	Initialize(ctx context.Context, userID string) error
}

type service struct {
	Repository               dreamboard.Repository
	FinancialSheetRepository financialsheet.Repository
}

func New(repository dreamboard.Repository, financialSheetRepository financialsheet.Repository) Service {
	return &service{
		Repository:               repository,
		FinancialSheetRepository: financialSheetRepository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error) {
	currentTime := time.Now()
	dreamboard.CreatedAt = currentTime
	dreamboard.UpdatedAt = currentTime
	dreamboard.ComputeTotals() // Initialize the computed fields

	dreamboardID, err := s.Repository.Create(ctx, dreamboard)
	if err != nil {
		return "", err
	}

	return dreamboardID, err
}

func (s *service) Find(ctx context.Context, id string) (*model.Dreamboard, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard id", errors.BadRequest, err)
	}

	dreamboard, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
	if err != nil {
		return nil, err
	}

	if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
		// Update computed fields before returning
		dreamboard.ID = dreamboard.ObjectID.Hex()
		dreamboard.CalculateSavedAmount(financialsheet)
		dreamboard.ComputeTotals()
	}

	for _, dream := range dreamboard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	return dreamboard, nil
}

func (s *service) FindAll(ctx context.Context) ([]*model.Dreamboard, error) {
	dreamboards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		// Get SavedAmount from Financial Sheet Balance
		financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
		if err != nil {
			return nil, err
		}

		if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
			// Update computed fields before returning
			dreamboard.ID = dreamboard.ObjectID.Hex()
			dreamboard.CalculateSavedAmount(financialsheet)
			dreamboard.ComputeTotals()
		}

		for _, dream := range dreamboard.Dreams {
			if dream != nil && !dream.ObjectID.IsZero() {
				dream.ID = dream.ObjectID.Hex()
			}
		}
	}

	return dreamboards, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error) {
	dreamboard, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		dreamboard, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, dreamboard.User)
	if err != nil {
		return nil, err
	}

	if dreamboard != nil && !dreamboard.ObjectID.IsZero() {
		// Update computed fields before returning
		dreamboard.ID = dreamboard.ObjectID.Hex()
		dreamboard.CalculateSavedAmount(financialsheet)
		dreamboard.ComputeTotals()
	}

	for _, dream := range dreamboard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	return dreamboard, nil
}

func (s *service) Update(ctx context.Context, dreamboard *model.Dreamboard) error {
	if err := dreamboard.Validate(); err != nil {
		return err
	}

	if dreamboard.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(dreamboard.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid dreamboard ID", errors.Validation, err)
		}
		dreamboard.ObjectID = objID
	}

	dreamboard.UpdatedAt = time.Now()
	dreamboard.ComputeTotals() // Update computed fields before saving

	err := s.Repository.Update(ctx, dreamboard)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	return s.Repository.Delete(ctx, objID)
}

// Category CRUD
func (s *service) CreateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	// Check for duplicate identifier
	for _, existing := range dreamboard.Categories {
		if existing.Identifier == category.Identifier {
			return nil, errors.New(errors.Service, "category identifier already exists", errors.Conflict, nil)
		}
	}

	if err := s.Repository.CreateCategory(ctx, dreamboard.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) FindCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) (*model.Category, error) {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
	}

	category, err := s.Repository.FindCategory(ctx, dreamboard.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if category != nil && !category.ObjectID.IsZero() {
		category.ID = category.ObjectID.Hex()
	}

	return category, nil
}

func (s *service) UpdateCategory(ctx context.Context, dreamboard *model.Dreamboard, category *model.Category) (*model.Category, error) {
	if err := category.Validate(); err != nil {
		return nil, err
	}

	if category.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(category.ID)
		if err != nil {
			return nil, errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
		}
		category.ObjectID = objID
	}

	// Check for duplicate identifier (excluding the current category)
	for _, existing := range dreamboard.Categories {
		if existing.Identifier == category.Identifier && existing.ObjectID != category.ObjectID {
			return nil, errors.New(errors.Service, "category identifier already exists", errors.Conflict, nil)
		}
	}

	if err := s.Repository.UpdateCategory(ctx, dreamboard.ObjectID, category); err != nil {
		return nil, err
	}

	category.ID = category.ObjectID.Hex()
	return category, nil
}

func (s *service) DeleteCategory(ctx context.Context, dreamboard *model.Dreamboard, categoryID string) error {
	objID, err := primitive.ObjectIDFromHex(categoryID)
	if err != nil {
		return errors.New(errors.Service, "invalid category ID", errors.BadRequest, err)
	}

	// Check if category is being used by any dream
	categoryToDelete, err := s.FindCategory(ctx, dreamboard, categoryID)
	if err != nil {
		return err
	}

	for _, dream := range dreamboard.Dreams {
		if dream.Category.String() == categoryToDelete.Identifier {
			return errors.New(errors.Service, "category is in use by one or more dreams", errors.Conflict, nil)
		}
	}

	return s.Repository.DeleteCategory(ctx, dreamboard.ObjectID, objID)
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	}

	if existing != nil {
		return errors.New(errors.Service, "dreamboard already exists", errors.Conflict, nil)
	}

	currentTime := time.Now()
	newBoard := &model.Dreamboard{
		User:       userID,
		Dreams:     []*model.Dream{},
		Categories: []*model.Category{}, // Add default categories after creation
		CreatedAt:  currentTime,
		UpdatedAt:  currentTime,
	}

	newBoard.ComputeTotals() // Initialize computed fields to zero since there are no dreams

	if err := newBoard.Validate(); err != nil {
		return err
	}

	boardID, err := s.Repository.Create(ctx, newBoard)
	if err != nil {
		return err
	}

	objID, err := primitive.ObjectIDFromHex(boardID)
	if err != nil {
		return errors.New(errors.Service, "invalid board ID", errors.Internal, err)
	}

	// Add default categories
	categories := []model.Category{
		model.Professional,
		model.Financial,
		model.Leisure,
		model.Emotional,
		model.Intellectual,
		model.Spiritual,
		model.Physical,
		model.Intimate,
		model.Social,
		model.Familial,
	}

	return s.Repository.CreateCategories(ctx, objID, categories)
}
