package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*model.Dreamboard, error)
	FindAll(ctx context.Context) ([]*model.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*model.Dreamboard, error)

	// Category Management
	FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*model.Category, error)
	// Dream Management
	FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*model.Dream, error)

	// Share Link Management
	FindShareLink(ctx context.Context, id primitive.ObjectID) (*model.ShareLink, error)
	FindShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error)
	FindShareLinkByDreamID(ctx context.Context, dreamID string) (*model.ShareLink, error)

	// Contribution Management
	FindContribution(ctx context.Context, id primitive.ObjectID) (*model.Contribution, error)
	FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
	FindContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error)
	FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error)
	FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, dreamboard *model.Dreamboard) (string, error)
	CreateDelete(ctx context.Context, deletedDreamboard *model.DeletedDreamboard) error

	// Update operations
	Update(ctx context.Context, dreamboard *model.Dreamboard) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Category Management
	CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error
	CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []model.Category) error
	UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *model.Category) error
	DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error
	// Dream Management
	CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error
	UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *model.Dream) error
	RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error

	// Share Link Management
	CreateShareLink(ctx context.Context, shareLink *model.ShareLink) (string, error)
	UpdateShareLink(ctx context.Context, shareLink *model.ShareLink) error
	DeleteShareLink(ctx context.Context, id primitive.ObjectID) error

	// Contribution Management
	CreateContribution(ctx context.Context, contribution *model.Contribution) (string, error)
	UpdateContribution(ctx context.Context, contribution *model.Contribution) error
	UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status model.ContributionStatus) error
	DeleteContribution(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
