package dreamboard

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"os"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Share Link Management

// CreateShareLink creates a new share link for a dream
func (s *service) CreateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	// Check if share link already exists
	existing, err := s.Repository.FindShareLinkByDreamID(ctx, dreamID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil {
		return existing, nil
	}

	// Generate unique token
	token, err := generateToken()
	if err != nil {
		return nil, errors.New(errors.Service, "failed to generate token", errors.Internal, err)
	}

	shareLink := &model.ShareLink{
		DreamID:   dreamID,
		Token:     token,
		IsEnabled: true,
		ExpiresAt: time.Now().Add(365 * 24 * time.Hour), // 1 year expiration
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := shareLink.Validate(); err != nil {
		return nil, err
	}

	_, err = s.Repository.CreateShareLink(ctx, shareLink)
	if err != nil {
		return nil, err
	}

	shareLink.ID = shareLink.ObjectID.Hex()
	return shareLink, nil
}

// GetShareLink retrieves a share link by dream ID
func (s *service) GetShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	shareLink, err := s.Repository.FindShareLinkByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	if shareLink != nil && !shareLink.ObjectID.IsZero() {
		shareLink.ID = shareLink.ObjectID.Hex()
	}

	return shareLink, nil
}

// GetShareLinkByToken retrieves a share link by token
func (s *service) GetShareLinkByToken(ctx context.Context, token string) (*model.ShareLink, error) {
	shareLink, err := s.Repository.FindShareLinkByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	if shareLink != nil && !shareLink.ObjectID.IsZero() {
		shareLink.ID = shareLink.ObjectID.Hex()
	}

	return shareLink, nil
}

// UpdateShareLinkStatus updates the enabled status of a share link
func (s *service) UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error {
	shareLink, err := s.GetShareLink(ctx, dreamID)
	if err != nil {
		return err
	}

	shareLink.IsEnabled = isEnabled
	shareLink.UpdatedAt = time.Now()

	return s.Repository.UpdateShareLink(ctx, shareLink)
}

// RegenerateShareLink creates a new token for an existing share link
func (s *service) RegenerateShareLink(ctx context.Context, dreamID string) (*model.ShareLink, error) {
	shareLink, err := s.GetShareLink(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Generate new token
	newToken, err := generateToken()
	if err != nil {
		return nil, errors.New(errors.Service, "failed to generate token", errors.Internal, err)
	}

	shareLink.Token = newToken
	shareLink.UpdatedAt = time.Now()

	if err := s.Repository.UpdateShareLink(ctx, shareLink); err != nil {
		return nil, err
	}

	return shareLink, nil
}

// Invitation Management

// GetInviteDetails retrieves invitation details for a share link token
func (s *service) GetInviteDetails(ctx context.Context, token string) (*InviteDetailsDTO, error) {
	shareLink, err := s.GetShareLinkByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Get dream details
	dream, err := s.findDreamByID(ctx, shareLink.DreamID)
	if err != nil {
		return nil, err
	}

	// Get active contributions count
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, shareLink.DreamID)
	if err != nil {
		return nil, err
	}

	// TODO: Get creator user details from user service
	inviteDetails := &InviteDetailsDTO{
		DreamID:             shareLink.DreamID,
		DreamTitle:          dream.Title,
		CreatorUserID:       dream.CreatorUserID,
		CreatorUserName:     "Creator Name", // TODO: Get from user service
		CreatorAvatarURL:    "",             // TODO: Get from user service
		EstimatedCost:       dream.EstimatedCost,
		CurrentRaisedAmount: dream.CurrentRaisedAmount,
		ParticipantCount:    len(activeContributions),
		IsExpired:           shareLink.ExpiresAt.Before(time.Now()),
		IsEnabled:           shareLink.IsEnabled,
	}

	return inviteDetails, nil
}

// JoinSharedDream allows a user to join a shared dream
func (s *service) JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*model.Contribution, error) {
	shareLink, err := s.GetShareLinkByToken(ctx, token)
	if err != nil {
		return nil, err
	}

	// Validate share link
	if !shareLink.IsEnabled {
		return nil, errors.New(errors.Service, "invite link is disabled", errors.BadRequest, nil)
	}
	if shareLink.ExpiresAt.Before(time.Now()) {
		return nil, errors.New(errors.Service, "invite link has expired", errors.BadRequest, nil)
	}

	// Check if user already has an active contribution for this dream
	existing, err := s.Repository.FindContributionByDreamAndUser(ctx, shareLink.DreamID, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return nil, err
	}
	if existing != nil && existing.Status == model.ContributionStatusActive {
		return nil, errors.New(errors.Service, "user already contributing to this dream", errors.Conflict, nil)
	}

	// Create new contribution
	contribution := &model.Contribution{
		DreamID:              shareLink.DreamID,
		ContributorUserID:    userID,
		IsCreator:            isCreator,
		MonthlyPledgedAmount: monthlyPledgedAmount,
		Status:               model.ContributionStatusActive,
		JoinedAt:             time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := contribution.Validate(); err != nil {
		return nil, err
	}

	_, err = s.Repository.CreateContribution(ctx, contribution)
	if err != nil {
		return nil, err
	}

	contribution.ID = contribution.ObjectID.Hex()
	return contribution, nil
}

// FindPersonalDreams retrieves all personal (non-shared) dreams for a user
func (s *service) FindPersonalDreams(ctx context.Context, userID string) ([]*model.Dream, error) {
	dreamboard, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	var personalDreams []*model.Dream
	for _, dream := range dreamboard.Dreams {
		if !dream.IsShared {
			personalDreams = append(personalDreams, dream)
		}
	}

	return personalDreams, nil
}

// FindSharedDreams retrieves all shared dreams where the user is creator or active contributor
func (s *service) FindSharedDreams(ctx context.Context, userID string) ([]*model.Dream, error) {
	// Get user's contributions
	contributions, err := s.GetContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	var sharedDreams []*model.Dream
	dreamIDs := make(map[string]bool) // To avoid duplicates

	// Get dreams where user is an active contributor
	for _, contribution := range contributions {
		if contribution.Status == model.ContributionStatusActive {
			if !dreamIDs[contribution.DreamID] {
				// Find the dream in the user's dreamboard or other users' dreamboards
				dream, err := s.findDreamByID(ctx, contribution.DreamID)
				if err == nil && dream != nil {
					sharedDreams = append(sharedDreams, dream)
					dreamIDs[contribution.DreamID] = true
				}
			}
		}
	}

	return sharedDreams, nil
}

// Helper functions

// generateToken creates a cryptographically secure random token
func generateToken() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateInviteURL creates a shareable invite URL using the APP_URL environment variable
func generateInviteURL(token string) string {
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "http://localhost:8080" // fallback to development URL
	}
	return fmt.Sprintf("%s/dreamboards/dreams/join?code=%s", appURL, token)
}
