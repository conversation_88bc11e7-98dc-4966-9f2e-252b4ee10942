package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Dream CRUD

// CreateDream creates a new dream in a dreamboard
func (s *service) CreateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) (*CreateDreamResponseDTO, error) {
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range dreamboard.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.CreatedAt = time.Now()
	dream.UpdatedAt = dream.CreatedAt
	dream.Color = s.getColor(dream.Category.String())

	// Set default values for shared dream fields
	if dream.IsShared {
		dream.CreatorUserID = dreamboard.User
		dream.FundingStatus = model.FundingStatusSharedOpenForParticipants
		dream.CurrentRaisedAmount = 0

		// Calculate initial duration
		if dream.MonthlySavings > 0 {
			duration := int(dream.EstimatedCost / dream.MonthlySavings)
			dream.CalculatedDurationMonths = &duration
		}
	} else {
		dream.FundingStatus = model.FundingStatusPersonalActive
		dream.CurrentRaisedAmount = 0
	}

	if err := s.Repository.CreateDream(ctx, dreamboard.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard with the new dreams
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we added a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	// Find the newly created dream by ID
	var createdDream *model.Dream
	for _, d := range updatedBoard.Dreams {
		if d.ID == dream.ID {
			createdDream = d
			break
		}
	}

	// Prepare response
	response := &CreateDreamResponseDTO{
		Dream: createdDream,
	}

	// If it's a shared dream, create ShareLink and creator's Contribution
	if dream.IsShared {
		if dream.ID != "" {
			// Create ShareLink
			sharedLink, err := s.CreateShareLink(ctx, dream.ID)
			if err != nil {
				return nil, err
			}

			// Generate invite URL
			shareURL := generateInviteURL(sharedLink.Token)
			response.ShareURL = &shareURL

			// Create creator's contribution
			isCreator := true // Creator is also a contributor
			_, err = s.JoinSharedDream(ctx, sharedLink.Token, dreamboard.User, isCreator, dream.MonthlySavings)
			if err != nil {
				return nil, err
			}
		}
	}

	return response, nil
}

// FindDream retrieves a specific dream from a dreamboard
func (s *service) FindDream(ctx context.Context, dreamboard *model.Dreamboard, dreamID string) (*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	dream, err := s.Repository.FindDream(ctx, dreamboard.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if dream != nil && !dream.ObjectID.IsZero() {
		dream.ID = dream.ObjectID.Hex()
	}

	return dream, nil
}

// UpdateDream updates an existing dream in a dreamboard
func (s *service) UpdateDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	// Validate dream
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range dreamboard.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.ObjectID = objID
	dream.UpdatedAt = time.Now()

	if err := s.Repository.UpdateDream(ctx, dreamboard.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we updated a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

// RemoveDream removes a dream from a dreamboard
func (s *service) RemoveDream(ctx context.Context, dreamboard *model.Dreamboard, dream *model.Dream) ([]*model.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	dream.ObjectID = objID
	if err := s.Repository.RemoveDream(ctx, dreamboard.ObjectID, dream.ObjectID); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, dreamboard.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we removed a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

// findDreamByID is a helper method to find a dream by ID across all dreamboards
func (s *service) findDreamByID(ctx context.Context, dreamID string) (*model.Dream, error) {
	// This is a simplified implementation
	// In a real scenario, we might need a more efficient way to find dreams by ID
	// across all dreamboards, possibly with a dedicated index or query

	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	// For now, we'll need to search through dreamboards
	// This is not efficient and should be optimized in production
	dreamboards, err := s.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		for _, dream := range dreamboard.Dreams {
			if dream.ObjectID == objID {
				return dream, nil
			}
		}
	}

	return nil, errors.New(errors.Service, "dream not found", errors.NotFound, nil)
}

// Helper function to get color for a category
func (s *service) getColor(category string) string {
	switch category {
	case model.Professional.Identifier:
		return model.Professional.Color
	case model.Financial.Identifier:
		return model.Financial.Color
	case model.Leisure.Identifier:
		return model.Leisure.Color
	case model.Emotional.Identifier:
		return model.Emotional.Color
	case model.Intellectual.Identifier:
		return model.Intellectual.Color
	case model.Spiritual.Identifier:
		return model.Spiritual.Color
	case model.Physical.Identifier:
		return model.Physical.Color
	case model.Intimate.Identifier:
		return model.Intimate.Color
	case model.Social.Identifier:
		return model.Social.Color
	case model.Familial.Identifier:
		return model.Familial.Color
	default:
		return model.UndefinedCategory.Color
	}
}
