package dreamboard

import (
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// CreateDreamResponseDTO represents the response when creating a dream
type CreateDreamResponseDTO struct {
	Dream    *model.Dream `json:"dream"`
	ShareURL *string      `json:"shareUrl,omitempty"`
}

// InviteDetailsDTO contains information about a shared dream invitation
type InviteDetailsDTO struct {
	DreamID             string          `json:"dreamId"`
	DreamTitle          string          `json:"dreamTitle"`
	DreamDescription    string          `json:"dreamDescription,omitempty"`
	CreatorUserID       string          `json:"creatorUserId"`
	CreatorUserName     string          `json:"creatorUserName"`
	CreatorAvatarURL    string          `json:"creatorAvatarUrl"`
	EstimatedCost       monetary.Amount `json:"estimatedCost"`
	CurrentRaisedAmount monetary.Amount `json:"currentRaisedAmount"`
	ParticipantCount    int             `json:"participantCount"`
	IsExpired           bool            `json:"isExpired"`
	IsEnabled           bool            `json:"isEnabled"`
}

// DreamDashboardDTO contains comprehensive information about a shared dream
type DreamDashboardDTO struct {
	DreamDetails     DreamDetailsDTO   `json:"dreamDetails"`
	Contributors     []ContributorDTO  `json:"contributors"`
	RecentActivity   []ActivityDTO     `json:"recentActivity"`
	MonthlyBreakdown []MonthlyDataDTO  `json:"monthlyBreakdown"`
}

// DreamDetailsDTO contains basic information about a dream
type DreamDetailsDTO struct {
	ID                      string          `json:"id"`
	Title                   string          `json:"title"`
	IsCreator               bool            `json:"isCreator"`
	TotalCost               monetary.Amount `json:"totalCost"`
	RaisedAmount            monetary.Amount `json:"raisedAmount"`
	RemainingAmount         monetary.Amount `json:"remainingAmount"`
	EstimatedDurationMonths *int            `json:"estimatedDurationMonths"`
	FundingStatus           string          `json:"fundingStatus"`
	ShareLinkDetails        ShareLinkDTO    `json:"shareLinkDetails"`
}

// ShareLinkDTO contains information about a dream's share link
type ShareLinkDTO struct {
	Token     string `json:"token"`
	IsEnabled bool   `json:"isEnabled"`
	ShareURL  string `json:"shareUrl"`
}

// ContributorDTO contains information about a dream contributor
type ContributorDTO struct {
	UserID                        string          `json:"userId"`
	UserName                      string          `json:"userName"`
	UserAvatarURL                 string          `json:"userAvatarUrl"`
	IsCreatorFlag                 bool            `json:"isCreatorFlag"`
	MonthlyPledgedAmount          monetary.Amount `json:"monthlyPledgedAmount"`
	CurrentMonthPaidAmount        monetary.Amount `json:"currentMonthPaidAmount"`
	PledgePaidPercentageThisMonth float64         `json:"pledgePaidPercentageThisMonth"`
}

// ActivityDTO represents an activity entry in the dream dashboard
type ActivityDTO struct {
	Type        string          `json:"type"`
	UserID      string          `json:"userId"`
	UserName    string          `json:"userName"`
	Amount      monetary.Amount `json:"amount,omitempty"`
	Description string          `json:"description"`
	Timestamp   string          `json:"timestamp"`
}

// MonthlyDataDTO represents monthly contribution data
type MonthlyDataDTO struct {
	Month              string          `json:"month"`
	TotalPledged       monetary.Amount `json:"totalPledged"`
	TotalPaid          monetary.Amount `json:"totalPaid"`
	CompletionRate     float64         `json:"completionRate"`
	ActiveContributors int             `json:"activeContributors"`
}
