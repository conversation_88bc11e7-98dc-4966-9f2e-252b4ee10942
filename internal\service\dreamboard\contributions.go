package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Contribution Management

// GetContributionsByDreamID retrieves all contributions for a specific dream
func (s *service) GetContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	contributions, err := s.Repository.FindContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// GetActiveContributionsByDreamID retrieves only active contributions for a specific dream
func (s *service) GetActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	contributions, err := s.Repository.FindActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// GetContributionsByUserID retrieves all contributions for a specific user
func (s *service) GetContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error) {
	contributions, err := s.Repository.FindContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	for _, contribution := range contributions {
		if contribution != nil && !contribution.ObjectID.IsZero() {
			contribution.ID = contribution.ObjectID.Hex()
		}
	}

	return contributions, nil
}

// UpdateContributionStatus updates the status of all contributions for a dream
func (s *service) UpdateContributionStatus(ctx context.Context, dreamID string, status model.ContributionStatus) error {
	return s.Repository.UpdateContributionStatusByDreamID(ctx, dreamID, status)
}

// UpdateContribution updates a specific contribution
func (s *service) UpdateContribution(ctx context.Context, contribution *model.Contribution) error {
	if err := contribution.Validate(); err != nil {
		return err
	}

	if contribution.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(contribution.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid contribution ID", errors.Validation, err)
		}
		contribution.ObjectID = objID
	}

	return s.Repository.UpdateContribution(ctx, contribution)
}

// DeleteContribution deletes a contribution
func (s *service) DeleteContribution(ctx context.Context, contributionID string) error {
	objID, err := primitive.ObjectIDFromHex(contributionID)
	if err != nil {
		return errors.New(errors.Service, "invalid contribution ID", errors.BadRequest, err)
	}

	return s.Repository.DeleteContribution(ctx, objID)
}

// Dream Management

// CalculateDreamDuration calculates the estimated duration in months for a dream
func (s *service) CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error) {
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	var totalMonthlyPledged monetary.Amount
	for _, contribution := range activeContributions {
		totalMonthlyPledged += contribution.MonthlyPledgedAmount
	}

	if totalMonthlyPledged == 0 {
		return nil, nil // Cannot calculate duration with zero contributions
	}

	duration := int(estimatedCost / totalMonthlyPledged)
	return &duration, nil
}

// GetDreamDashboard retrieves comprehensive dashboard information for a shared dream
func (s *service) GetDreamDashboard(ctx context.Context, dreamID string, userID string) (*DreamDashboardDTO, error) {
	// Get active contributions
	activeContributions, err := s.GetActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Check if user is a contributor
	isCreator := false
	for _, contribution := range activeContributions {
		if contribution.ContributorUserID == userID {
			isCreator = contribution.IsCreator
			break
		}
	}

	// Get share link details
	shareLink, err := s.GetShareLink(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	shareLinkDetails := ShareLinkDTO{
		Token:     shareLink.Token,
		IsEnabled: shareLink.IsEnabled,
		ShareURL:  generateInviteURL(shareLink.Token),
	}

	// Build contributors list
	contributors := make([]ContributorDTO, 0, len(activeContributions))
	for _, contribution := range activeContributions {
		// TODO: Get user details from user service
		// TODO: Get current month paid amount from financial sheet service
		contributor := ContributorDTO{
			UserID:                        contribution.ContributorUserID,
			UserName:                      "User Name", // TODO: Get from user service
			UserAvatarURL:                 "",          // TODO: Get from user service
			IsCreatorFlag:                 contribution.IsCreator,
			MonthlyPledgedAmount:          contribution.MonthlyPledgedAmount,
			CurrentMonthPaidAmount:        0, // TODO: Get from financial sheet service
			PledgePaidPercentageThisMonth: 0, // TODO: Calculate
		}
		contributors = append(contributors, contributor)
	}

	// TODO: Get recent activity from financial sheet service
	recentActivity := []ActivityDTO{} // TODO: Implement

	// TODO: Get monthly breakdown from financial sheet service
	monthlyBreakdown := []MonthlyDataDTO{} // TODO: Implement

	// TODO: Get dream details from dreamboard service
	// TODO: Get transaction history from financial sheet service
	dreamDetails := DreamDetailsDTO{
		ID:                      dreamID,
		Title:                   "Dream Title", // TODO: Get from dream
		IsCreator:               isCreator,
		TotalCost:               0,   // TODO: Get from dream
		RaisedAmount:            0,   // TODO: Get from financial sheet service
		RemainingAmount:         0,   // TODO: Calculate
		EstimatedDurationMonths: nil, // TODO: Calculate
		FundingStatus:           "",  // TODO: Get from dream
		ShareLinkDetails:        shareLinkDetails,
	}

	dashboard := &DreamDashboardDTO{
		DreamDetails:     dreamDetails,
		Contributors:     contributors,
		RecentActivity:   recentActivity,
		MonthlyBreakdown: monthlyBreakdown,
	}

	return dashboard, nil
}

// GetContributionByDreamAndUser retrieves a specific contribution by dream and user
func (s *service) GetContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error) {
	contribution, err := s.Repository.FindContributionByDreamAndUser(ctx, dreamID, userID)
	if err != nil {
		return nil, err
	}

	if contribution != nil && !contribution.ObjectID.IsZero() {
		contribution.ID = contribution.ObjectID.Hex()
	}

	return contribution, nil
}

// CreateContribution creates a new contribution
func (s *service) CreateContribution(ctx context.Context, contribution *model.Contribution) (*model.Contribution, error) {
	if err := contribution.Validate(); err != nil {
		return nil, err
	}

	_, err := s.Repository.CreateContribution(ctx, contribution)
	if err != nil {
		return nil, err
	}

	contribution.ID = contribution.ObjectID.Hex()
	return contribution, nil
}
