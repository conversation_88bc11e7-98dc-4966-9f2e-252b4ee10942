package dreamboard

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/labstack/echo/v4"
)

// RegisterContributionRoutes registers contribution related routes
func (dc *controller) RegisterContributionRoutes(ctx context.Context, currentGroup *echo.Group) {
	// Contributions routes
	contributionsGroup := currentGroup.Group("/dreamboards/contributions", middlewares.AuthGuard())
	
	// Contribution Management
	contributionsGroup.GET("/dreams/:dreamId", dc.GetContributionsByDreamID())
	contributionsGroup.GET("/users/me", dc.GetMyContributions())
	contributionsGroup.PUT("/:contributionId", dc.UpdateContribution())
	contributionsGroup.DELETE("/:contributionId", dc.DeleteContribution())
	contributionsGroup.PUT("/dreams/:dreamId/status", dc.UpdateContributionStatus())
}

// GetContributionsByDreamID retrieves all contributions for a specific dream
func (dc *controller) GetContributionsByDreamID() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		// Check if user wants only active contributions
		activeOnly := c.QueryParam("active") == "true"

		var contributions []*model.Contribution
		var err error

		if activeOnly {
			contributions, err = dc.Service.GetActiveContributionsByDreamID(ctx, dreamID)
		} else {
			contributions, err = dc.Service.GetContributionsByDreamID(ctx, dreamID)
		}

		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, contributions)
	}
}

// GetMyContributions retrieves all contributions for the authenticated user
func (dc *controller) GetMyContributions() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		contributions, err := dc.Service.GetContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, contributions)
	}
}

// UpdateContribution updates an existing contribution
func (dc *controller) UpdateContribution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		contributionID := c.Param("contributionId")
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if contributionID == "" {
			return errors.New(errors.Controller, "contribution ID is required", errors.BadRequest, nil)
		}

		var request struct {
			MonthlyPledgedAmount monetary.Amount           `json:"monthlyPledgedAmount"`
			Status               model.ContributionStatus `json:"status"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// Get existing contribution to verify ownership
		// This is a simplified approach - in production you might want to add proper authorization
		contributions, err := dc.Service.GetContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var targetContribution *model.Contribution
		for _, contribution := range contributions {
			if contribution.ID == contributionID {
				targetContribution = contribution
				break
			}
		}

		if targetContribution == nil {
			return errors.New(errors.Controller, "contribution not found or access denied", errors.NotFound, nil)
		}

		// Update the contribution
		if request.MonthlyPledgedAmount > 0 {
			targetContribution.MonthlyPledgedAmount = request.MonthlyPledgedAmount
		}
		if request.Status != "" {
			targetContribution.Status = request.Status
		}

		err = dc.Service.UpdateContribution(ctx, targetContribution)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, targetContribution)
	}
}

// DeleteContribution deletes a contribution
func (dc *controller) DeleteContribution() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		contributionID := c.Param("contributionId")
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if contributionID == "" {
			return errors.New(errors.Controller, "contribution ID is required", errors.BadRequest, nil)
		}

		// Verify ownership before deletion
		contributions, err := dc.Service.GetContributionsByUserID(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var hasAccess bool
		for _, contribution := range contributions {
			if contribution.ID == contributionID {
				hasAccess = true
				break
			}
		}

		if !hasAccess {
			return errors.New(errors.Controller, "contribution not found or access denied", errors.NotFound, nil)
		}

		err = dc.Service.DeleteContribution(ctx, contributionID)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}

// UpdateContributionStatus updates the status of all contributions for a dream
func (dc *controller) UpdateContributionStatus() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId")

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		var request struct {
			Status model.ContributionStatus `json:"status" validate:"required"`
		}

		if err := c.Bind(&request); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		if !request.Status.IsValid() {
			return errors.New(errors.Controller, "invalid contribution status", errors.BadRequest, nil)
		}

		err := dc.Service.UpdateContributionStatus(ctx, dreamID, request.Status)
		if err != nil {
			return err
		}

		return c.NoContent(http.StatusNoContent)
	}
}
