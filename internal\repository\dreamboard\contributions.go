package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// Contribution Read Operations

// FindContribution retrieves a contribution by ID
func (m *mongoDB) FindContribution(ctx context.Context, id primitive.ObjectID) (*model.Contribution, error) {
	var contribution model.Contribution
	err := m.contributionsCollection.FindOne(ctx, bson.M{"_id": id}).Decode(&contribution)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "contribution not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find contribution", errors.Internal, err)
	}
	return &contribution, nil
}

// FindContributionsByDreamID retrieves all contributions for a specific dream
func (m *mongoDB) FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	cursor, err := m.contributionsCollection.Find(ctx, bson.M{"dreamId": dreamID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find contributions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err := cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contributions = append(contributions, &contribution)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error", errors.Internal, err)
	}

	return contributions, nil
}

// FindContributionsByUserID retrieves all contributions for a specific user
func (m *mongoDB) FindContributionsByUserID(ctx context.Context, userID string) ([]*model.Contribution, error) {
	cursor, err := m.contributionsCollection.Find(ctx, bson.M{"contributorUserId": userID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find contributions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err := cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contributions = append(contributions, &contribution)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error", errors.Internal, err)
	}

	return contributions, nil
}

// FindContributionByDreamAndUser retrieves a specific contribution by dream and user
func (m *mongoDB) FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*model.Contribution, error) {
	var contribution model.Contribution
	filter := bson.M{
		"dreamId":           dreamID,
		"contributorUserId": userID,
	}
	
	err := m.contributionsCollection.FindOne(ctx, filter).Decode(&contribution)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "contribution not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find contribution", errors.Internal, err)
	}
	return &contribution, nil
}

// FindActiveContributionsByDreamID retrieves only active contributions for a specific dream
func (m *mongoDB) FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*model.Contribution, error) {
	filter := bson.M{
		"dreamId": dreamID,
		"status":  model.ContributionStatusActive,
	}
	
	cursor, err := m.contributionsCollection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find active contributions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var contributions []*model.Contribution
	for cursor.Next(ctx) {
		var contribution model.Contribution
		if err := cursor.Decode(&contribution); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode contribution", errors.Internal, err)
		}
		contributions = append(contributions, &contribution)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error", errors.Internal, err)
	}

	return contributions, nil
}

// Contribution Write Operations

// CreateContribution creates a new contribution
func (m *mongoDB) CreateContribution(ctx context.Context, contribution *model.Contribution) (string, error) {
	contribution.JoinedAt = time.Now()
	contribution.UpdatedAt = contribution.JoinedAt

	insertedResult, err := m.contributionsCollection.InsertOne(ctx, contribution)
	if err != nil {
		return "", errors.New(errors.Repository, "failed to create contribution", errors.Internal, err)
	}
	
	contribution.ObjectID = insertedResult.InsertedID.(primitive.ObjectID)
	return contribution.ObjectID.Hex(), nil
}

// UpdateContribution updates an existing contribution
func (m *mongoDB) UpdateContribution(ctx context.Context, contribution *model.Contribution) error {
	contribution.UpdatedAt = time.Now()

	filter := bson.M{"_id": contribution.ObjectID}
	update := bson.M{"$set": contribution}

	result, err := m.contributionsCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update contribution", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "contribution not found", errors.NotFound, nil)
	}

	return nil
}

// UpdateContributionStatusByDreamID updates the status of all contributions for a dream
func (m *mongoDB) UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status model.ContributionStatus) error {
	filter := bson.M{"dreamId": dreamID}
	update := bson.M{
		"$set": bson.M{
			"status":    status,
			"updatedAt": time.Now(),
		},
	}

	_, err := m.contributionsCollection.UpdateMany(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update contribution status", errors.Internal, err)
	}

	return nil
}

// DeleteContribution deletes a contribution by ID
func (m *mongoDB) DeleteContribution(ctx context.Context, id primitive.ObjectID) error {
	result, err := m.contributionsCollection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete contribution", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "contribution not found", errors.NotFound, nil)
	}

	return nil
}
